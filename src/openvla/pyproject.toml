[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
name = "openvla"
authors = [
    {name = "<PERSON><PERSON>", email="<EMAIL>"},
    {name = "<PERSON>", email="<EMAIL>"},
    {name = "<PERSON><PERSON><PERSON>", email="<EMAIL>"},
]
description = "OpenVLA: Vision-Language-Action Models for Robotics"
version = "0.0.3"
readme = "README.md"
requires-python = ">=3.8"
keywords = ["vision-language-actions models", "multimodal pretraining", "robot learning"]
license = {file = "LICENSE"}
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3 :: Only",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
dependencies = [
    "accelerate>=0.25.0",
    "draccus==0.8.0",
    "einops",
    # "flash_attn==2.5.5",      # Here for documentation -- install *AFTER* editable install (follow README)
    "huggingface_hub",
    "json-numpy",
    "jsonlines",
    "matplotlib",
    "peft==0.11.1",
    "protobuf",
    "rich",
    "sentencepiece==0.1.99",
    "timm==0.9.10",
    "tokenizers==0.21.1",
    "torch==2.2.0",
    "torchvision==0.17.0",
    "torchaudio==2.2.0",
    "transformers @ git+https://github.com/siyuhsu/transformers.git@vla-cache-openvla",  # IMPORTANT: Use this fork for vla-cache support
    "wandb",
    "scikit-image==0.25.0",
    "tensorflow==2.15.0",
    "tensorflow_datasets==4.9.3",
    "tensorflow_graphics==2021.12.3",
    "dlimp @ git+https://github.com/moojink/dlimp_openvla"
]

[project.optional-dependencies]
dev = [
    "black>=24.2.0",
    "gpustat",
    "ipython",
    "pre-commit",
    "ruff>=0.2.2",
]
sagemaker = [
    "boto3",
    "sagemaker"
]

[project.urls]
homepage = "https://github.com/openvla/openvla"
repository = "https://github.com/openvla/openvla"
documentation = "https://github.com/openvla/openvla"

[tool.setuptools.packages.find]
where = ["."]
exclude = ["cache"]

[tool.setuptools.package-data]
"prismatic" = ["py.typed"]

[tool.black]
line-length = 121
target-version = ["py38", "py39", "py310"]
preview = true

[tool.ruff]
line-length = 121
target-version = "py38"

[tool.ruff.lint]
select = ["A", "B", "E", "F", "I", "RUF", "W"]
ignore = ["F722"]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["E402", "F401"]
